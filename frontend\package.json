{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@radix-ui/react-collection": "^1.1.7", "@radix-ui/react-compose-refs": "^1.1.2", "@radix-ui/react-context": "^1.1.2", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-direction": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@tanstack/react-query": "^5.90.2", "axios": "^1.12.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.475.0", "next": "15.4.2", "next-themes": "^0.4.6", "react": "^19.2.0", "react-dom": "^19.2.0", "react-hook-form": "^7.63.0", "recharts": "^3.2.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.1.11"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.14", "@types/node": "^20.19.19", "@types/react": "^19.2.0", "@types/react-dom": "^19.2.0", "eslint": "^9.36.0", "eslint-config-next": "15.1.6", "postcss": "^8.5.6", "tailwindcss": "^4.1.14", "tw-animate-css": "^1.4.0", "typescript": "^5.9.3"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0", "description": "A Next.js project: frontend", "author": "d<PERSON><PERSON>"}